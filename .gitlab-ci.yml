image: uniqdev/android-fastlane:android-jdk17

# Reusable templates
.notify: &notify
  after_script:
    - |
      if [ $CI_JOB_STATUS == "success" ]; then
        curl -X POST -H 'Content-type: application/json' --data "{\"text\":\"✅ Pipeline succeeded in $CI_PROJECT_NAME\"}" $SLACK_WEBHOOK_URL
      else
        curl -X POST -H 'Content-type: application/json' --data "{\"text\":\"❌ Pipeline failed in $CI_PROJECT_NAME\"}" $SLACK_WEBHOOK_URL
      fi

.version_check: &version_check
  before_script:
    - |
      if [[ ! "$CI_COMMIT_TAG" =~ ^v[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
        echo "Invalid version format. Must be vX.Y.Z"
        exit 1
      fi

.basic_android_setup: &basic_android_setup
  before_script:
    - bundle update
    - bundle env
    - export GRADLE_USER_HOME=$(pwd)/.gradle
    - chmod +x ./gradlew
    - echo ${GOOGLE_PLAY_KEY} | base64 -d > app/keys.json
    - echo ${KEYSTORE_FILE} | base64 -d > app/uniqkeystore.jks
    - echo ${KEYSTORE_PROPERTIES} | base64 -d > keystore.properties

# Optimized cache configuration
cache:
  key:
    files:
      - gradle/wrapper/gradle-wrapper.properties
      - app/build.gradle
  paths:
    - .gradle/wrapper
    - .gradle/caches
    - app/.gradle
    - $GEM_HOME
    - build
    - sdk

# Pipeline stages
stages:
  - build
  - test
  - qa
  - internal
  - release
  - production

# Variables for different environments
variables:
  GRADLE_OPTS: "-Dorg.gradle.daemon=false"

.prod_variables: &prod_variables
  variables:
    ENVIRONMENT: "production"
    APP_VARIANT: "release"

.staging_variables: &staging_variables
  variables:
    ENVIRONMENT: "staging"
    APP_VARIANT: "staging"

# Build jobs
build:
  stage: build
  <<: *basic_android_setup
  timeout: 1h
  script:
    - cat $FASTLANE_ENV > fastlane/.env
    - bundle exec fastlane developmentDeploy
  artifacts:
    public: true
    expire_in: 1 week
    reports:
      junit: app/build/test-results/**/TEST-*.xml
    paths:
      - app/build/outputs/apk
      - app/build/reports
  rules:
#    - if: $CI_COMMIT_BRANCH == "testing"
#      when: always
    - when: manual

buildApkRelease:
  stage: build
  <<: *basic_android_setup
  # <<: *version_check
  timeout: 2h
  script:
    - fastlane buildApkRelease
  artifacts:
    when: always
    expire_in: 2 weeks
    paths:
      - app/build/outputs/
      - app/build/reports/
  rules:
    - if: $CI_COMMIT_BRANCH == "master"
      when: manual
    - if: $CI_COMMIT_BRANCH == "staging"
      when: manual
    - if: $CI_COMMIT_BRANCH == "dev"
      when: manual

# QA jobs
qa_checks:
  stage: qa
  needs: ["build"]
  <<: *basic_android_setup
  script:
    - ./gradlew test
    - ./gradlew lint
  artifacts:
    reports:
      junit: app/build/test-results/**/TEST-*.xml
    paths:
      - app/build/reports/
  rules:
    - if: $CI_COMMIT_BRANCH =~ /(master|staging|testing)/

# Internal deployment jobs
stagingUpload:
  stage: internal
  # needs: ["qa_checks"]
  <<: *basic_android_setup
  <<: *staging_variables
  <<: *notify
  retry:
    max: 2
    when:
      - runner_system_failure
      - stuck_or_timeout_failure
  script:
    - bundle exec fastlane stagingDeploy
  artifacts:
    when: always
    paths:
      - app/build/outputs/bundle/
  rules:
    - if: $CI_COMMIT_BRANCH == "staging"
      when: manual
    - if: $CI_PIPELINE_SOURCE == "schedule"
      when: always

# Release jobs
upload_to_play_store:
  stage: release
  needs: ["qa_checks"]
  <<: *basic_android_setup
  # <<: *version_check
  <<: *notify
  retry:
    max: 2
    when:
      - runner_system_failure
      - stuck_or_timeout_failure
  script:
    -  bundle exec fastlane betaDeploy
  rules:
    - if: $CI_COMMIT_TAG
      when: manual

# Production deployment jobs
productionUpload:
  stage: production
  needs: ["qa_checks"]
  <<: *basic_android_setup
  <<: *prod_variables
  <<: *notify
  retry:
    max: 2
    when:
      - runner_system_failure
      - stuck_or_timeout_failure
  script:
    - bundle exec fastlane betaDeploy
  artifacts:
    when: always
    expire_in: 2 weeks
    paths:
      - app/build/outputs/apk/
      - app/build/outputs/bundle/
  rules:
    - if: $CI_COMMIT_BRANCH == "master"
      when: manual
    - if: $CI_COMMIT_BRANCH == "master-fix"
      when: manual
    - if: $CI_PIPELINE_SOURCE == "schedule"
      when: always

# Cleanup job
cleanup_job:
  stage: .post
  script:
    - rm -rf app/build/outputs/
  rules:
    - when: always