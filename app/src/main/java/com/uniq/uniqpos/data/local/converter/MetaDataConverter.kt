package com.uniq.uniqpos.data.local.converter

import androidx.room.TypeConverter
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken

/**
 * Type converter for Map<String, Any> to handle meta_data field in OrderSalesEntity
 */
class MetaDataConverter {
    @TypeConverter
    fun fromMapToJson(value: Map<String, Any>?): String? {
        return if (value == null) null else Gson().toJson(value)
    }

    @TypeConverter
    fun fromJsonToMap(value: String?): Map<String, Any>? {
        return if (value == null) null else {
            val mapType = object : TypeToken<Map<String, Any>>() {}.type
            Gson().fromJson(value, mapType)
        }
    }
}
