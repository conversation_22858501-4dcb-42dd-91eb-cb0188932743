<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.READ_SYNC_SETTINGS" />
    <uses-permission android:name="android.permission.READ_SYNC_STATS" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.WRITE_SYNC_SETTINGS" />
    <uses-permission android:name="android.permission.AUTHENTICATE_ACCOUNTS" />
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_MULTICAST_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="READ_PRIVILEGED_PHONE_STATE" />
    <uses-permission android:name="android.permission.READ_PHONE_NUMBERS" />
    <uses-permission
        android:name="android.permission.READ_PHONE_STATE"
        android:maxSdkVersion="29" />

    <application
        android:name=".app.UniqApplication"
        android:allowBackup="true"
        android:fullBackupContent="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:largeHeap="true"
        android:networkSecurityConfig="@xml/network_security_config"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/AppThemeBridge">
        <activity
            android:name=".view.productcatalogue.AddProductMainActivity"
            android:label="@string/add_product"
            android:exported="false" />
        <activity
            android:name=".view.productcatalogue.ocr.ProductEditList"
            android:exported="false"
            android:label="@string/add_product" />
        <activity
            android:name=".view.payment.PaymentV2Activity"
            android:exported="false"
            android:label="@string/payment" />
        <activity
            android:name=".view.selforder.SelfOrderActivity"
            android:label="Self Order" />
        <activity
            android:name=".view.webview.WebViewActivity"
            android:label="@string/app_name" />
        <activity
            android:name=".view.billing.BillingInformationActivity"
            android:label="Billing Information" />
        <activity
            android:name=".view.billing.BillingActivity"
            android:label="Add Subscription" />
        <activity
            android:name=".view.closeshift.CloseShiftDetailActivity"
            android:label="Close Shift Detail" />
        <activity android:name=".view.simplepagelist.SimplePageListActivity" />
        <activity
            android:name=".view.ordersales.OrderSalesRejectActivity"
            android:label="Rejected Order" />
        <activity
            android:name=".view.ordersales.OrderSalesDetailActivity"
            android:label="Order Detail" />
        <activity android:name=".view.ordersales.OrderSalesActivity" />
        <activity
            android:name=".view.purchase.AddSupplierActivity"
            android:label="@string/add_supplier" />
        <activity
            android:name=".view.purchase.AddOperationalCostActivity"
            android:label="@string/operational_cost" />
        <activity
            android:name=".view.payment.PaymentQrActivity"
            android:theme="@style/AppTheme.NoActionBar" />

        <meta-data
            android:name="com.google.android.gms.version"
            android:value="@integer/google_play_services_version" />
        <meta-data
            android:name="com.google.android.gms.vision.DEPENDENCIES"
            android:value="barcode" />
        <meta-data
            android:name="io.fabric.ApiKey"
            android:value="1f1dee7bcc468dde19965fbf41780c5a7e34698f" />
        <meta-data
            android:name="com.bugsnag.android.API_KEY"
            android:value="b0448d94ab3e7480319d3eb3692364f8" />
        <meta-data
            android:name="preloaded_fonts"
            android:resource="@array/preloaded_fonts" />
        <meta-data
            android:name="android.support.PARENT_ACTIVITY"
            android:value=".view.main.MainActivity" />

        <service
            android:name=".service.UniqMessagingService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>
        <service
            android:name=".sync.UniqSyncAdapterService"
            android:exported="true">
            <intent-filter>
                <action android:name="android.content.SyncAdapter" />
            </intent-filter>

            <meta-data
                android:name="android.content.SyncAdapter"
                android:resource="@xml/syncadapter" />
        </service>
        <service
            android:name=".sync.UniqAuthenticatorService"
            android:exported="false">
            <intent-filter>
                <action android:name="android.accounts.AccountAuthenticator" />
            </intent-filter>

            <meta-data
                android:name="android.accounts.AccountAuthenticator"
                android:resource="@xml/authenticator" />
        </service>
        <service
            android:name=".sync.UniqJobService"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.BIND_JOB_SERVICE" />

        <provider
            android:name=".sync.StubContentProvider"
            android:authorities="@string/authority"
            android:exported="false"
            android:syncable="true" />
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

        <receiver
            android:name=".receiver.NetworkChangeReceiver"
            android:exported="false"
            android:label="NetworkChangeReceiver">
            <intent-filter>
                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
                <action android:name="android.net.wifi.WIFI_STATE_CHANGED" />
                <action android:name="android.bluetooth.device.action.ACL_CONNECTED" />
                <action android:name="android.bluetooth.device.action.ACL_DISCONNECT_REQUESTED" />
                <action android:name="android.bluetooth.device.action.ACL_DISCONNECTED" />
            </intent-filter>
        </receiver>

        <activity
            android:name=".view.splashscreen.SplashScreen"
            android:exported="true"
            android:theme="@style/SplashTheme">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name=".view.login.LoginAdminActivity"
            android:theme="@style/AppTheme.NoActionBarAndTitleBar" />
        <activity
            android:name=".view.register.RegisterActivity"
            android:theme="@style/AppTheme.NoActionBarAndTitleBar" />
        <activity
            android:name=".view.chooseoutlet.ChooseOutletActivity"
            android:label="@string/choose_outlite" />
        <activity
            android:name=".view.chooseoperator.ChooseOperatorActivity"
            android:label="@string/operator"
            android:theme="@style/AppThemeMaterial" />
        <activity
            android:name=".view.verifypin.VerifyPINActivity"
            android:configChanges="keyboardHidden"
            android:windowSoftInputMode="stateAlwaysHidden" />
        <activity
            android:name=".view.main.MainActivity"
            android:label="@string/title_activity_main"
            android:theme="@style/AppTheme.NoActionBarBridge"
            android:windowSoftInputMode="stateHidden">
            <meta-data
                android:name="android.app.searchable"
                android:resource="@xml/searchable" />
        </activity>
        <activity
            android:name=".view.payment.PaymentActivity"
            android:label="@string/payment" />
        <activity
            android:name=".view.table.TableListActivity"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".view.cart.TransactionCartActivity"
            android:label="@string/pending_bill" />
        <activity
            android:name=".view.setting.printer.AddPrinterActivity"
            android:label="@string/find_printer" />
        <activity android:name=".view.transaction.MenuExtraActivity" />
        <activity
            android:name=".view.pendingprint.PendingPrintActivity"
            android:label="@string/pending_print" />
        <activity
            android:name=".view.runoutstock.RunOutOfStockActivity"
            android:label="@string/run_out" />
        <activity
            android:name=".view.member.MemberActivity"
            android:label="Member" />
        <activity
            android:name=".view.member.AddMemberActivity"
            android:label="Add Member" />
        <activity
            android:name=".view.transactionhistory.HistoryDetailActivity"
            android:label="@string/history_detail" />
        <activity
            android:name=".view.reservation.ReservationActivity"
            android:label="@string/reservation" />
        <activity
            android:name=".view.reservation.AddReservationActivity"
            android:label="@string/make_reservation" />
        <activity android:name=".view.setting.SettingDetailActivity" />
        <activity
            android:name=".view.ordersummary.OrderSummaryActivity"
            android:label="@string/order_summary" />
        <activity
            android:name=".view.payment.SplitBillActivity"
            android:label="Split Bill" />
        <activity
            android:name=".view.scanbarcode.ScanBarcodeActivity"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".view.productcatalogue.AddProductActivity"
            android:label="@string/add_product" />
        <activity
            android:name=".view.piutang.PiutangDetailActivity"
            android:label="Piutang Detail" />
        <activity
            android:name=".view.piutang.PiutangActivity"
            android:label="Piutang" />
        <activity
            android:name=".view.setting.printer.PrinterDetailActivity"
            android:label="Printer Detail" />
        <activity
            android:name=".view.login.code.LoginWithCodeActivity"
            android:label="Login With Code" />
    </application>

</manifest>