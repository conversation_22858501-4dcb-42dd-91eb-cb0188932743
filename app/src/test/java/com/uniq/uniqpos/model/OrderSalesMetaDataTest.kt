package com.uniq.uniqpos.model

import com.google.gson.Gson
import com.uniq.uniqpos.data.local.entity.OrderSalesEntity
import org.junit.Test
import org.junit.Assert.*

class OrderSalesMetaDataTest {

    @Test
    fun `OrderSalesEntity should serialize metaData to JSON correctly`() {
        // Given
        val metaData = mapOf(
            "payment_method" to "credit_card",
            "delivery_fee" to 5000,
            "special_instructions" to "Extra spicy",
            "is_priority" to true
        )
        
        val orderSalesEntity = OrderSalesEntity(
            orderSalesId = "ORDER001",
            timeOrder = System.currentTimeMillis(),
            items = "[]",
            orderType = "delivery",
            outletFkid = 1,
            status = "pending",
            metaData = metaData
        )

        // When
        val json = Gson().toJson(orderSalesEntity)

        // Then
        assertTrue(json.contains("meta_data"))
        assertTrue(json.contains("payment_method"))
        assertTrue(json.contains("credit_card"))
        assertTrue(json.contains("delivery_fee"))
        assertTrue(json.contains("5000"))
        assertTrue(json.contains("special_instructions"))
        assertTrue(json.contains("Extra spicy"))
        assertTrue(json.contains("is_priority"))
        assertTrue(json.contains("true"))
    }

    @Test
    fun `OrderSalesEntity should handle null metaData`() {
        // Given
        val orderSalesEntity = OrderSalesEntity(
            orderSalesId = "ORDER002",
            timeOrder = System.currentTimeMillis(),
            items = "[]",
            orderType = "pickup",
            outletFkid = 1,
            status = "pending",
            metaData = null
        )

        // When
        val json = Gson().toJson(orderSalesEntity)

        // Then
        // Should not crash and should serialize properly
        assertTrue(json.contains("ORDER002"))
        assertTrue(json.contains("pickup"))
        // meta_data should be null or not present
        assertTrue(!json.contains("meta_data") || json.contains("\"meta_data\":null"))
    }

    @Test
    fun `OrderSalesEntity should handle empty metaData map`() {
        // Given
        val orderSalesEntity = OrderSalesEntity(
            orderSalesId = "ORDER003",
            timeOrder = System.currentTimeMillis(),
            items = "[]",
            orderType = "dine_in",
            outletFkid = 1,
            status = "pending",
            metaData = emptyMap()
        )

        // When
        val json = Gson().toJson(orderSalesEntity)

        // Then
        assertTrue(json.contains("ORDER003"))
        assertTrue(json.contains("dine_in"))
        assertTrue(json.contains("meta_data"))
        assertTrue(json.contains("{}"))
    }

    @Test
    fun `OrderSalesEntity should handle complex metaData values`() {
        // Given
        val metaData = mapOf(
            "customer_preferences" to mapOf(
                "spice_level" to "medium",
                "allergies" to listOf("nuts", "dairy")
            ),
            "order_count" to 42,
            "discount_applied" to 15.5,
            "notes" to "Customer called to confirm order"
        )
        
        val orderSalesEntity = OrderSalesEntity(
            orderSalesId = "ORDER004",
            timeOrder = System.currentTimeMillis(),
            items = "[]",
            orderType = "delivery",
            outletFkid = 1,
            status = "pending",
            metaData = metaData
        )

        // When
        val json = Gson().toJson(orderSalesEntity)

        // Then
        assertTrue(json.contains("meta_data"))
        assertTrue(json.contains("customer_preferences"))
        assertTrue(json.contains("spice_level"))
        assertTrue(json.contains("medium"))
        assertTrue(json.contains("allergies"))
        assertTrue(json.contains("nuts"))
        assertTrue(json.contains("dairy"))
        assertTrue(json.contains("order_count"))
        assertTrue(json.contains("42"))
        assertTrue(json.contains("discount_applied"))
        assertTrue(json.contains("15.5"))
    }
}
