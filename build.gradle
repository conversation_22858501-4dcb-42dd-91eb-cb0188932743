// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    ext.kotlin_version = '1.6.0'
    repositories {
        google()
        // maven { url 'https://jitpack.io' }
//        jcenter()
        // maven { url 'https://maven.fabric.io/public' }
//        maven { url 'https://oss.sonatype.org/content/repositories/snapshots' }
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:8.13.0' //7.2.2 | 7.3.0 | 7.4.0 | 7.4.2
        classpath 'org.jetbrains.kotlin:kotlin-gradle-plugin:2.1.0' // 1.6.0 | 1.5.31 | 1.6.21 | 1.8.0
        classpath 'com.google.gms:google-services:4.3.15' // 4.3.15 | 4.4.0
        classpath 'com.google.firebase:firebase-crashlytics-gradle:2.9.9' // 2.8.1 | 2.9.0 | 2.9.9 | 3.0.2
        classpath "com.bugsnag:bugsnag-android-gradle-plugin:8.0.0"
        classpath "org.jetbrains.kotlin.plugin.compose:org.jetbrains.kotlin.plugin.compose.gradle.plugin:2.0.20"

//        classpath 'io.fabric.tools:gradle:1.30.0'        
//        classpath 'com.github.triplet.gradle:play-publisher:1.2.2'
//        classpath 'com.github.triplet.gradle:play-publisher:2.1.0-SNAPSHOT'        
    }
}

allprojects {
    repositories {
        google()
        jcenter()
//        maven { url 'https://maven.fabric.io/public' }
//https://maven.pkg.jetbrains.space/public/p/ktor/eap
        // maven { url 'https://dl.bintray.com/amulyakhare/maven' }
        maven { url 'https://jitpack.io' }
        maven { url "https://dl.bintray.com/kotlin/ktor" }
        maven { url "https://maven.pkg.jetbrains.space/public/p/ktor/eap" }
        maven { url = uri("https://maven.pkg.jetbrains.space/kotlin/p/kotlin/dev") }
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}

ext {
    lifecycle_version = '2.2.0'
    room_version = '2.7.2'
    recyclerViewVersion = '1.1.0'
    cardViewVersion = '1.0.0'
    koin_version = '2.0.1'

    retrofit_version = '2.6.4' //this is the maximum version works on kitkat
    okhttp_interceptor_version = '3.6.0'
    timber_version = '4.7.1'
    daggerVersion = '2.23.2'

    glideVersion = '4.11.0'
}
