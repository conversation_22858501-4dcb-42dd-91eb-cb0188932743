package com.uniq.uniqpos.view.ordersales

import androidx.lifecycle.viewModelScope
import com.google.gson.Gson
import com.uniq.uniqpos.data.local.entity.*
import com.uniq.uniqpos.data.remote.model.Employee
import com.uniq.uniqpos.data.remote.model.Outlet
import com.uniq.uniqpos.data.remote.model.ShiftOpen
import com.uniq.uniqpos.data.remote.repository.ProductRepository
import com.uniq.uniqpos.data.remote.repository.SalesRepository
import com.uniq.uniqpos.data.remote.repository.SettingRepository
import com.uniq.uniqpos.util.*
import com.uniq.uniqpos.util.lifecycle.BaseViewModel
import com.uniq.uniqpos.util.lifecycle.SingleLiveEvent
import kotlinx.coroutines.*
import timber.log.Timber
import javax.inject.Inject

/**
 * Created by annasblackhat on 2019-07-25
 */
class OrderSalesViewModel @Inject constructor(
    private val salesRepository: SalesRepository,
    private var settingRepository: SettingRepository,
    private var productRepository: ProductRepository
) : BaseViewModel() {

//    private val viewModelJob = Job()
//    private val uiScope = CoroutineScope(Dispatchers.Main + viewModelJob)

    val dialogTaskCommand = SingleLiveEvent<String>()
    val printTask = SingleLiveEvent<List<PendingPrintEntity>>()

    val taskNavigateOrderSalesDetail = SingleLiveEvent<OrderSalesEntity>()
    val taskNavigateSalesDetail = SingleLiveEvent<SalesEntity>()

    val orderSalesList = ArrayList<OrderSalesEntity>()
    val productList = ArrayList<ProductEntity>()
    val gratuityList = ArrayList<GratuityEntity>()
    private val tmpSalesConverted = HashMap<String, SalesEntity>()

    fun getOrderSalesLive(outletId: Int) = salesRepository.getOrderSalesLive(outletId)

    fun updateOrderSalesById(
        orderId: String,
        status: String,
        info: String?,
        outlet: Outlet,
        employee: Employee,
        shift: ShiftOpen
    ) {
        orderSalesList.firstOrNull { it.orderSalesId == orderId }?.let { order ->
            when (status) {
                "accept" -> order.timeAcceptReject = System.currentTimeMillis()
                "ready" -> order.timeReady = System.currentTimeMillis()
                "taken" -> order.timeTaken = System.currentTimeMillis()
                "reject" -> {
                    order.timeAcceptReject = System.currentTimeMillis()
                    order.rejectReason = info
                }
            }
            order.synced = false
            order.status = status

            var isError = false
//            //save to sales
//            if (status == "accept") {
//                val sales: SalesEntity? = try {
//                    Gson().fromJson(order.items, SalesEntity::class.java)
//                } catch (e: Exception) {
//                    dialogTaskCommand.postValue("Can not recognize sales data")
//                    Timber.i("converting to salesEntity error - $e ==> ${order.items}")
//                    isError = true
//                    null
//                }
//
//                val noNota = Utils.generateNoNota()
//                sales?.let { sales ->
//                    viewModelScope.launch {
//                        //val salesCount = salesRepository.countSalesToday()
////                        sales.displayNota = generateDisplayNota(outlet.adminFkid, outlet.outletId, salesCount)
//                        sales.noNota = noNota
//                        sales.employeeID = employee.employeeId
//                        sales.employeeName = employee?.name
//                        sales.openShiftId = shift.openShiftId
//                        sales.table = ""
//                        sales.synced = false
//                        sales.outletID = outlet.outletId ?: 0
//                        sales.outletName = outlet.name
//
//                        Timber.i("save sales [order] -> ${sales.noNota}")
//                        salesRepository.saveSales(sales)
//                    }
//                    order.salesFkid = noNota
//
//                    //print order
//                    viewModelScope.launch {
//                        val printers = settingRepository.getPrinterList()
//                        Timber.i("printer size : ${printers.size}")
//                        val ticketList = settingRepository.getPrinterTicketOrders()
//                        val printDataList = NotaManager.createNota(
//                            printers, ticketList, sales, outlet, employee, true
//                        )
//                        printTask.value = printDataList
//                    }
//                }
//            }

            if (!isError) {
                Timber.i("update this : ${Gson().toJson(order.copy(items = ""))}")
//                uiScope.launch { salesRepository.updateOrderSales(order) }
//                viewModelScope.launch { salesRepository.updateOrderSales(order) }
                viewModelScope.launch { salesRepository.updateOrderSalesStatus(order, employee.employeeId, info) }
            }
        }
    }

    fun setData(items: List<OrderSalesEntity>) {
        orderSalesList.clear()
        Timber.i("total order sales: ${items.size}")
        items.firstOrNull()?.let { item ->
            Timber.i("first item: ${Gson().toJson(item)}")
        }
        Timber.i("order sales count: ${items.groupingBy { it.status }.eachCount()}")
        viewModelScope.launch {
            items.forEach {
                try {
                    var sales: SalesEntity
                    if (tmpSalesConverted.containsKey(it.orderSalesId)) {
                        sales = tmpSalesConverted[it.orderSalesId]!!
                    } else {
                        sales = Gson().fromJson(it.items, SalesEntity::class.java)
                        //handle if product name to set (from backend)
                        sales.orderList?.forEach { item ->
                            if (item.product?.name.safe().isEmpty()) {
                                productList.firstOrNull { it.productDetailId == item.product?.productDetailId }
                                    ?.let { product ->
                                        item.product = product
                                    }
                            }
                        }
                    }

                    //cache it
                    tmpSalesConverted[it.orderSalesId] = sales
                    val orderAdjusted = it.adjustData()
                    it.customer = orderAdjusted.customer
                    it.grandTotal = orderAdjusted.grandTotal
                    it.itemNames = orderAdjusted.itemNames
                } catch (e: Exception) {
                    it.customer = "Error"
                    Timber.i("convert error - $e")
                }
            }
        }
        orderSalesList.addAll(items)
    }

    fun savePendingPrint(pendingPrintEntity: List<PendingPrintEntity>) {
        viewModelScope.launch {
            salesRepository.savePendingPrint(pendingPrintEntity)
        }
    }

    fun syncOrderSales(outletId: Int) {
        viewModelScope.launch(Dispatchers.IO) {
            salesRepository.syncOrderSales(outletId)
        }
    }

    fun fetchSalesEntity(orderSales: OrderSalesEntity) {
        viewModelScope.launch {
            val salesList = salesRepository.getSalesByIds(listOf(orderSales.orderSalesId))
            Timber.i("fetching salesEntity '${orderSales.orderSalesId}', is in history? ${salesList.isNotEmpty()}, status ${orderSales.status}")
            if (salesList.isNotEmpty()) {
                val sales = salesList.takeIf { it.size > 1 }?.firstOrNull { it.status == "Refund" }
                    ?: salesList.first()
                sales.salesTag = null
                taskNavigateSalesDetail.postValue(sales)
            } else {
                taskNavigateOrderSalesDetail.postValue(orderSales)
            }
        }
    }

    fun loadProduct() {
        viewModelScope.launch {
            productList.clear()
            val products = awaitBackgroundList { productRepository.getProduct() }
            productList.addAll(products)
        }
    }

    fun loadGratuity(){
        viewModelScope.launch {
            gratuityList.clear()
            val gratuities = awaitBackgroundList { productRepository.getGratuityList() }
            gratuityList.addAll(gratuities)
        }
    }

    fun getUpdateStatusAvailable(currentStatus: String, orderType: String): List<String> {
        if (currentStatus == "pending") {
            return listOf("Accept", "Reject")
        }else if(currentStatus == "payment_verified"){
            return if(orderType == "pickup") listOf("Send") else listOf("Ready")
        }else if(currentStatus == "ready"){
            return if(orderType == "pickup") listOf("Delivered") else listOf("Taken")
        }
        return listOf()
    }

}