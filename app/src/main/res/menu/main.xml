<?xml version="1.0" encoding="utf-8"?>
<menu xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <item
        android:id="@+id/action_order_sales"
        android:title="App Orders" />

    <item
        android:id="@+id/action_pending_print"
        android:title="Pending Print" />

    <item
        android:id="@+id/action_promotion"
        android:orderInCategory="100"
        android:title="@string/promotion"
        android:visible="false"
        app:showAsAction="never" />
    <item
        android:id="@+id/action_transaction_total"
        android:orderInCategory="100"
        android:title="@string/transaction_total"
        android:visible="false"
        app:showAsAction="never" />
    <item
        android:id="@+id/action_reservation"
        android:orderInCategory="100"
        android:title="@string/reservation"
        android:visible="false"
        app:showAsAction="never" />
    <item
        android:id="@+id/action_damage"
        android:orderInCategory="100"
        android:title="@string/damage"
        android:visible="false"
        app:showAsAction="never" />
    <item
        android:id="@+id/action_run_out"
        android:orderInCategory="100"
        android:title="@string/run_out"
        app:showAsAction="never" />

    <item
        android:id="@+id/action_product"
        android:orderInCategory="100"
        android:title="@string/add_product"
        app:showAsAction="never" />

    <item
        android:id="@+id/action_piutang"
        android:orderInCategory="100"
        android:title="Piutang"
        app:showAsAction="never" />

    <item android:id="@+id/action_sync"
        android:orderInCategory="100"
        android:title="@string/sync_data"
        app:showAsAction="never"/>

    <item
        android:id="@+id/action_send_report"
        android:orderInCategory="100"
        android:title="Send Report"
        app:showAsAction="never" />

</menu>
