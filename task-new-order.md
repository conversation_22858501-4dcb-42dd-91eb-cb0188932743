### **Prompt for Your AI Coding Assistant**

**Role:** You are an expert Android developer specializing in Jetpack Compose and interoperability with existing XML-based projects.

**Project Context:**
I am working on an Android Point of Sale (POS) application that was originally built using XML and Data Binding. I am now incrementally adding new features using Jetpack Compose.

**Primary Goal:**
Your task is to create the Jetpack Compose code for a "New Online Order" dialog. This dialog will be displayed modally within an existing Activity that uses an XML layout. The design should closely follow the provided visual specifications.

**Detailed UI Design Specifications:**

Please implement a composable function named `NewOrderDialog` that recreates the following design:

1.  **Dialog Container:** Use a standard `AlertDialog` as the main container. It should be modal and non-dismissible by tapping outside (this can be configured in the `onDismissRequest` property).

2.  **Header/Title:**
    *   The title slot of the `AlertDialog` should contain a `Row`.
    *   Inside the `Row`, include a bell emoji "🔔" as a `Text` element and another `Text` element with the string "New Online Order".
    *   The title text should be `FontSize` of `20.sp` and `FontWeight.SemiBold`.

3.  **Main Content (The `text` slot of the `AlertDialog`):**
    This section will be a `Column` containing the following, in order:

    *   **Customer Information:**
        *   A `Text` for the customer's name (e.g., "Eleanor Vance"). Make it large and bold (`FontSize` `22.sp`, `FontWeight.Bold`).
        *   A `Text` for the customer's phone number directly below the name. Make it smaller and lighter (`FontSize` `16.sp`, `color = Color.Gray`).

    *   **Order Metadata:**
        *   A `Row` with `Arrangement.SpaceBetween` to display the Order ID and Table Number.
        *   Use `Text` composables with labels, like "Order #: **#E-7B3D9**" and "Table: **12A**". The value part should be bold.
        *   This section should only be displayed if the table number is available.

    *   **Divider:** A horizontal `Divider` to separate the info from the item list.

    *   **Itemized List:**
        *   **Header Row:** A `Row` with `Text` for "Qty", "Item", and "Subtotal". Use a `weight` modifier on the "Item" `Text` to make it fill the available space. The "Subtotal" should be right-aligned. Use a subtle, all-caps style (`fontSize = 12.sp`, `color = Color.Gray`).
        *   **List:** Use a `Column` to list the order items. For each item, create a `Row` that aligns with the header:
            *   **Quantity:** A `Text` with a fixed width, bold.
            *   **Item Details:** A `Column` with `weight(1f)`. It contains:
                *   The main item name (`Text`, `FontWeight.Medium`).
                *   Any modifiers (e.g., "+ Extra pickles, no onions") as another `Text` below it, indented, smaller, and with a gray color.
            *   **Subtotal:** A `Text` for the price, right-aligned.

    *   **Divider:** Another `Divider`.

    *   **Total Section:**
        *   A `Row` with `Arrangement.SpaceBetween`.
        *   On the left, a `Text` with "Total" (`FontSize` `18.sp`, `FontWeight.Bold`).
        *   On the right, a `Text` with the final price, even larger and bolder (`FontSize` `22.sp`, `FontWeight.Bold`).

4.  **Action Buttons (Footer):**
    *   **Reject Button (`dismissButton`):** A `TextButton` with the text "Reject". The text color should be a destructive red (e.g., `Color(0xFFD32F2F)`).
    *   **Accept Button (`confirmButton`):** A filled `Button` with the text "Accept". The background color should be a positive green (e.g., `Color(0xFF2E7D32)`).

**Data Structures:**

To support this UI, please define the following Kotlin `data class`es. The composable function should accept an `Order` object as a parameter.

```kotlin
data class Customer(val name: String, val phone: String)
data class OrderItem(
    val quantity: Int,
    val name: String,
    val modifiers: List<String>,
    val subtotal: Double
)
data class Order(
    val id: String,
    val customer: Customer,
    val items: List<OrderItem>,
    val table: String?, // Nullable for orders without a table
    val total: Double
)
```

**Functional Requirements:**

*   The `NewOrderDialog` composable function signature should be:
    `fun NewOrderDialog(order: Order, onAccept: () -> Unit, onReject: () -> Unit, onDismissRequest: () -> Unit)`
*   `onAccept`: Lambda to be invoked when the "Accept" button is clicked.
*   `onReject`: Lambda to be invoked when the "Reject" button is clicked.
*   `onDismissRequest`: This should probably be linked to the `onReject` action, as we don't want the user to dismiss the dialog casually.

**Deliverables:**

1.  The complete Kotlin code for the `NewOrderDialog` composable function.
2.  The definitions for the `Customer`, `OrderItem`, and `Order` data classes.
3.  A small, self-contained example of how to *use* this `NewOrderDialog` within another composable. This example should include:
    *   Creating a sample `Order` data object.
    *   Using a `mutableStateOf` to control the dialog's visibility.
    *   A `Button` to trigger the dialog to appear.
    *   The conditional logic: `if (showDialog) { NewOrderDialog(...) }`

Please ensure the code is clean, idiomatic, and follows modern Jetpack Compose best practices.